#include "main.h" 

//A0Ϊ��������ת��ʱ��

//A6Ϊ�ϱߣ���ת��ʱ��

float position_1=0,position_2=0;

int main(void)
{
	// ��ʼ��
	USART1_Init(115200);
	USART2_Init(115200);
	
	//��ʼ���������
	Stepper_Init();
	
	//�ȹر�ʶ�𣬳�ʼ��DMA
	Discern_end();
	USART2_DMA_Init();

	//��ʼʶ��
	Discern_start();
	
	Stepper_Motor1_SetPosition(position_1);
	Stepper_Motor2_SetPosition(position_2);

	while (1)
	{
		Key_port();
		
		//��ȡ���ݸ��±�־
		if(USART_Get_RedFlag())
		{
			position_1 -= ERR_X;
			position_2 += ERR_Y;
			
			Stepper_Motor1_SetPosition(position_1);
			Stepper_Motor2_SetPosition(position_2);
		}
		
		
		
	}
}

