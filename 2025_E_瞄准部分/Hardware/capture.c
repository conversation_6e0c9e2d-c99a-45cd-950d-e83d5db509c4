#include "capture.h"

void Capture_Init(void)
{
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);	//����GPIO��ʱ��
	
	/*GPIO��ʼ��*/
	GPIO_InitTypeDef GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOA, &GPIO_InitStructure);
	
	GPIO_WriteBit(GPIOA,GPIO_Pin_4,Bit_SET);
}

void Capture_ON(void)
{
	GPIO_WriteBit(GPIOA,GPIO_Pin_4,Bit_RESET);
}

void Capture_OFF(void)
{
	GPIO_WriteBit(GPIOA,GPIO_Pin_4,Bit_SET);
}



