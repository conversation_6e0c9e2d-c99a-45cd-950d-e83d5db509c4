#include "PWM.h"


// ���״̬�ṹ��
typedef struct {
    int32_t current_position;    // ��ǰλ��
    int32_t target_position;     // Ŀ��λ��
    uint8_t is_moving;          // �Ƿ������˶�
    uint8_t direction;          // �˶����� (1=����, 0=����)
    uint8_t pulse_counter;      // ���������
} Motor_t;

// ȫ�ֱ���
volatile Motor_t motor1 = {0, 0, 0, 1, 0};
volatile Motor_t motor2 = {0, 0, 0, 1, 0};

/**
 * @brief  �������ϵͳ��ʼ��
 * @param  None
 * @retval None
 */
void Stepper_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStructure;
    NVIC_InitTypeDef NVIC_InitStructure;

    // ʹ��GPIOAʱ��
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);

    // ����Ϊ�������
    GPIO_InitStructure.GPIO_Pin = STEPPER_STEP1_PIN | STEPPER_STEP2_PIN |
                                  STEPPER_DIR1_PIN | STEPPER_DIR2_PIN;
	
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(STEPPER_GPIO_PORT, &GPIO_InitStructure);

    // ��ʼ������״̬
    GPIO_ResetBits(STEPPER_GPIO_PORT, STEPPER_STEP1_PIN | STEPPER_STEP2_PIN);
    GPIO_ResetBits(STEPPER_GPIO_PORT, STEPPER_DIR1_PIN | STEPPER_DIR2_PIN);

    // ʹ��TIM2ʱ��
    RCC_APB1PeriphClockCmd(STEPPER_TIMER_RCC, ENABLE);

    // ���ö�ʱ����������
    // 72MHz / 72 = 1MHz������Ƶ��Ϊ1MHz
    // 1000us = 1ms���ڣ���Ӧ1000HzƵ��
    TIM_TimeBaseInitStructure.TIM_Period = (uint16_t)(1000000 / STEPPER_SPEED - 1);  // �����ٶȼ�������
    TIM_TimeBaseInitStructure.TIM_Prescaler = 72 - 1;     // Ԥ��Ƶ��
    TIM_TimeBaseInitStructure.TIM_ClockDivision = TIM_CKD_DIV1;
    TIM_TimeBaseInitStructure.TIM_CounterMode = TIM_CounterMode_Up;
    TIM_TimeBaseInit(STEPPER_TIMER, &TIM_TimeBaseInitStructure);

    // ʹ�ܶ�ʱ���ж�
    TIM_ITConfig(STEPPER_TIMER, TIM_IT_Update, ENABLE);

    // ����NVIC
    NVIC_InitStructure.NVIC_IRQChannel = STEPPER_TIMER_IRQ;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

    // Ĭ�Ϲرն�ʱ��
    TIM_Cmd(STEPPER_TIMER, ENABLE);
}

/**
 * @brief  ���õ��1Ŀ��λ��
 * @param  position: Ŀ��λ�ã����������������ţ�
 * @retval None
 */
void Stepper_Motor1_SetPosition(int32_t position)
{
    motor1.target_position = position;

    // ���÷���
    if (position > motor1.current_position) 
	{
        motor1.direction = 1;  // ����
        GPIO_SetBits(STEPPER_GPIO_PORT, STEPPER_DIR1_PIN);
    } 
	else if (position < motor1.current_position) 
	{
        motor1.direction = 0;  // ����
        GPIO_ResetBits(STEPPER_GPIO_PORT, STEPPER_DIR1_PIN);
    }

    // ���Ŀ��λ���뵱ǰλ�ò�ͬ����ʼ�˶�
    if (position != motor1.current_position) 
	{
        motor1.is_moving = 1;
    } 
	else 
	{
        motor1.is_moving = 0;
    }
}

/**
 * @brief  ���õ��2Ŀ��λ��
 * @param  position: Ŀ��λ�ã����������������ţ�
 * @retval None
 */
void Stepper_Motor2_SetPosition(int32_t position)
{
    motor2.target_position = position;
	
    // ���÷���
    if (position > motor2.current_position) 
	{
        motor2.direction = 1;  // ����
        GPIO_SetBits(STEPPER_GPIO_PORT, STEPPER_DIR2_PIN);
    } else if (position < motor2.current_position) 
	{
        motor2.direction = 0;  // ����
        GPIO_ResetBits(STEPPER_GPIO_PORT, STEPPER_DIR2_PIN);
    }

    // ���Ŀ��λ���뵱ǰλ�ò�ͬ����ʼ�˶�
    if (position != motor2.current_position) 
	{
        motor2.is_moving = 1;
    } else 
	{
        motor2.is_moving = 0;
    }
}

/**
 * @brief  ������������
 * @param  motor: ���ָ��
 * @param  step_pin: ��������
 * @retval None
 */
static void Generate_Step_Pulse_1(void)
{
    // ���㵽Ŀ��ľ���
    int32_t distance = (motor1.direction == 1) ? 
        (motor1.target_position - motor1.current_position) : 
        (motor1.current_position - motor1.target_position);

    // ���ٿ��ƣ����ݾ�������Ƿ���������
    uint8_t skip_count = 1; // Ĭ��ÿ�ζ�������
    if (distance <= DECEL_DISTANCE && distance > 0) {
        // �������Ƶ�ʼ��������������
        uint8_t max_skip = STEPPER_SPEED / MIN_STEP_HZ; // 2000/200 = 10
        // ���ݾ������Լ�����������
        skip_count = 1 + ((DECEL_DISTANCE - distance) * (max_skip - 1)) / DECEL_DISTANCE;
        if (skip_count > max_skip) skip_count = max_skip;
    }

    motor1.pulse_counter++;
    
    // ֻ�м������ﵽ��������ʱ�ŷ�����
    if (motor1.pulse_counter >= skip_count) {
        motor1.pulse_counter = 0; // ���ü�����
        
        // �������壺�ߵ�ƽ
        GPIO_SetBits(STEPPER_GPIO_PORT, STEPPER_STEP1_PIN);
        Delay_us(STEPPER_PULSE_WIDTH);
        GPIO_ResetBits(STEPPER_GPIO_PORT, STEPPER_STEP1_PIN);

        // ����λ��
        if (motor1.direction == 1) {
            motor1.current_position++;
        } else {
            motor1.current_position--;
        }
    }

    // ����Ƿ񵽴�Ŀ��λ��
    if (motor1.current_position == motor1.target_position) {
        motor1.is_moving = 0;
        motor1.pulse_counter = 0; // ���ü�����
    }
}

static void Generate_Step_Pulse_2(void)
{
    // ���㵽Ŀ��ľ���
    int32_t distance = (motor2.direction == 1) ? 
        (motor2.target_position - motor2.current_position) : 
        (motor2.current_position - motor2.target_position);

    // ���ٿ��ƣ����ݾ�������Ƿ���������
    uint8_t skip_count = 1; // Ĭ��ÿ�ζ�������
    if (distance <= DECEL_DISTANCE && distance > 0) {
        // �������Ƶ�ʼ��������������
        uint8_t max_skip = STEPPER_SPEED / MIN_STEP_HZ; // 2000/200 = 10
        // ���ݾ������Լ�����������
        skip_count = 1 + ((DECEL_DISTANCE - distance) * (max_skip - 1)) / DECEL_DISTANCE;
        if (skip_count > max_skip) skip_count = max_skip;
    }

    motor2.pulse_counter++;
    
    // ֻ�м������ﵽ��������ʱ�ŷ�����
    if (motor2.pulse_counter >= skip_count) {
        motor2.pulse_counter = 0; // ���ü�����
        
        // �������壺�ߵ�ƽ
        GPIO_SetBits(STEPPER_GPIO_PORT, STEPPER_STEP2_PIN);
        Delay_us(STEPPER_PULSE_WIDTH);
        GPIO_ResetBits(STEPPER_GPIO_PORT, STEPPER_STEP2_PIN);

        // ����λ��
        if (motor2.direction == 1) {
            motor2.current_position++;
        } else {
            motor2.current_position--;
        }
    }

    // ����Ƿ񵽴�Ŀ��λ��
    if (motor2.current_position == motor2.target_position) {
        motor2.is_moving = 0;
        motor2.pulse_counter = 0; // ���ü�����
    }
}

/****************************************************************************
��	�ã���ʱ��2�жϷ�����

��	������

����ֵ����
****************************************************************************/
void TIM2_IRQHandler(void)
{
	if (TIM_GetITStatus(TIM2, TIM_IT_Update) == SET)
	{
		// ������1
		if (motor1.is_moving) 
		{
			Generate_Step_Pulse_1();
		}
		// ������2
		if (motor2.is_moving) 
		{
			Generate_Step_Pulse_2();
		}
		
		TIM_ClearITPendingBit(TIM2, TIM_IT_Update);
	}
}
