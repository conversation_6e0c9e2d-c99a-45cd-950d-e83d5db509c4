#include "USART_1.h"//���Դ���

#define USART1_Len    500	//�������ݵ���󳤶�


uint8_t USART1_RxFlag;					//����������ݰ���־λ

char USART1_RxPacket[USART1_Len];	//����������ݰ�����
char USART1_RxData[USART1_Len];		//�����鿴

/**
  * ��    �������ڳ�ʼ��
  * ��    ������
  * �� �� ֵ����
  */
void USART1_Init(uint32_t Baud_Rate)
{
	/*����ʱ��*/
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1, ENABLE);	//����USART1��ʱ��
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);	//����GPIOA��ʱ��
	
	/*GPIO��ʼ��*/
	GPIO_InitTypeDef GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOA, &GPIO_InitStructure);					//��PA9���ų�ʼ��Ϊ�����������
	
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOA, &GPIO_InitStructure);					//��PA10���ų�ʼ��Ϊ��������
	
	/*USART��ʼ��*/
	USART_InitTypeDef USART_InitStructure;					//����ṹ�����
	USART_InitStructure.USART_BaudRate = Baud_Rate;				//������
	USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;	//Ӳ�������ƣ�����Ҫ
	USART_InitStructure.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;	//ģʽ������ģʽ�ͽ���ģʽ��ѡ��
	USART_InitStructure.USART_Parity = USART_Parity_No;		//��żУ�飬����Ҫ
	USART_InitStructure.USART_StopBits = USART_StopBits_1;	//ֹͣλ��ѡ��1λ
	USART_InitStructure.USART_WordLength = USART_WordLength_8b;		//�ֳ���ѡ��8λ
	USART_Init(USART1, &USART_InitStructure);				//���ṹ���������USART_Init������USART1
	
	/*�ж��������*/
	USART_ITConfig(USART1, USART_IT_RXNE, ENABLE);			//�������ڽ������ݵ��ж�
	
	/*NVIC�жϷ���*/
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);			//����NVICΪ����2
	
	/*NVIC����*/
	NVIC_InitTypeDef NVIC_InitStructure;					//����ṹ�����
	NVIC_InitStructure.NVIC_IRQChannel = USART1_IRQn;		//ѡ������NVIC��USART1��
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;			//ָ��NVIC��·ʹ��
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;		//ָ��NVIC��·����ռ���ȼ�Ϊ2
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 2;		//ָ��NVIC��·����Ӧ���ȼ�Ϊ2
	NVIC_Init(&NVIC_InitStructure);							//���ṹ���������NVIC_Init������NVIC����
	
	/*USARTʹ��*/
	USART_Cmd(USART1, ENABLE);								//ʹ��USART1�����ڿ�ʼ����
}


/**
  * ��    �������ڷ���һ���ֽ�
  * ��    ����Byte Ҫ���͵�һ���ֽ�
  * �� �� ֵ����
  */
void USART1_SendByte(uint8_t Byte)
{
	USART_SendData(USART1, Byte);		//���ֽ�����д�����ݼĴ�����д���USART�Զ�����ʱ����
	while (USART_GetFlagStatus(USART1, USART_FLAG_TXE) == RESET);	//�ȴ��������
	/*�´�д�����ݼĴ������Զ����������ɱ�־λ���ʴ�ѭ�������������־λ*/
}

/****************************************************************************
��	�ã���ȡ���ڽ������ݰ���־λ

��	������

����ֵ��0 or 1	���յ����ݰ��󣬱�־λ��1����ȡ���־λ�Զ�����
****************************************************************************/
uint8_t USART1_GetRxFlag(void)
{
	if (USART1_RxFlag == 1)			//�����־λΪ1
	{
		USART1_RxFlag = 0;
		return 1;					//�򷵻�1�����Զ������־λ
	}
	return 0;						//�����־λΪ0���򷵻�0
}

/****************************************************************************
��	�ã�ʵ��printf()�������ܣ�����һ��

��	�������ɱ�������ַ���

����ֵ����
****************************************************************************/
#pragma import(__use_no_semihosting)

struct __FILE
{
	int handle;
};

FILE __stdout;
void _sys_exit(int x)
{
	x=x;
}

int fputc(int ch,FILE *f)
{
	USART_SendData(USART1,(uint8_t) ch);
	while(USART_GetFlagStatus(USART1, USART_FLAG_TXE)==RESET);
	return (ch);	
}

int fgetc(FILE *f)
{
	while(USART_GetFlagStatus(USART1, USART_FLAG_RXNE)==RESET);
	return ((int)USART_ReceiveData(USART1));	
}



/****************************************************************************
��	�ã������жϺ�����ͨ�����س��������������ݵķֽ���գ�

��	������

����ֵ����
****************************************************************************/
void USART1_IRQHandler(void)
{
	static uint8_t RxState = 0;		//�����ʾ��ǰ״̬��״̬�ľ�̬����
	static uint16_t pRxPacket = 0;	//�����ʾ��ǰ��������λ�õľ�̬����
	if (USART_GetITStatus(USART1, USART_IT_RXNE) == SET)		//�ж��Ƿ���USART3�Ľ����¼��������ж�
	{
		uint8_t RxData = USART_ReceiveData(USART1);				//��ȡ���ݼĴ���������ڽ��յ����ݱ���
		
		/*ʹ��״̬����˼·�����δ������ݰ��Ĳ�ͬ����*/
		
		if (RxState == 0)
		{
			USART1_RxPacket[pRxPacket] = RxData;	//�����ݴ������ݰ������ָ��λ��
			pRxPacket ++;				//���ݰ���λ������
			
			if (RxData == 0X0D)			//��⵽���ƻ��лس�����
			{
				RxState = 1;			//����һ��״̬
			}
		}
		
		else if (RxState == 1)
		{
			if (RxData == 0X0A)			//�������ȷʵ�ǻس�����
			{
				USART1_RxPacket[pRxPacket] = RxData;	//�����ݴ������ݰ������ָ��λ��
				pRxPacket ++;
				USART1_RxFlag = 1;		//�������ݰ���־λ��1���ɹ�����һ�����ݰ�
				
				USART1_RxPacket[pRxPacket-2]='\0';	//���ݽ�����־λ,�������س�����
				strcpy(USART1_RxData,USART1_RxPacket);//���������������ݴ����鿴
				pRxPacket=0;
				RxState=0;
			}//����������ݺ��ý�����ɱ�־
			
			else
			{
				USART1_RxPacket[pRxPacket] = RxData;	//�����ݴ������ݰ������ָ��λ��
				pRxPacket ++;
				
				RxState=0;//�����ع�״̬���������
			}//����������ݺ�����ع�״̬���������
		}
	
		USART_ClearITPendingBit(USART1, USART_IT_RXNE);		//�����־λ
	}
}
