#include "Timer.h"

void TIM7_Init(void)
{
    /* 1. ����TIM7ʱ�� */
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM7, ENABLE);
    
    /* 2. ����TIM7�������� */
    TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;
    TIM_TimeBaseStructure.TIM_Period = 0xFFFF;  // �Զ���װ��ֵ���ֵ
    TIM_TimeBaseStructure.TIM_Prescaler = 72 - 1; // 72��Ƶ��1MHz����Ƶ��(1us����)
    TIM_TimeBaseStructure.TIM_ClockDivision = TIM_CKD_DIV1;
    TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
    TIM_TimeBaseInit(TIM7, &TIM_TimeBaseStructure);
    
    /* 3. ʹ��TIM7 */
    TIM_Cmd(TIM7, ENABLE);
}


void TIM7_delay_us(uint16_t us)
{
    /* 1. ���ü����� */
    TIM7->CNT = 0;
    
    /* 2. �ȴ��������ﵽĿ��ֵ */
    while(TIM7->CNT < us);
}


/**
  * @brief  ���뼶��ʱ
  * @param  ms: Ҫ��ʱ�ĺ�����
  * @retval ��
  */
void TIM7_delay_ms(uint16_t ms)
{
    while(ms--)
    {
        TIM7_delay_us(1000); // ����1000us��ʱ
    }
}


