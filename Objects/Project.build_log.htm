<html>
<body>
<pre>
<h1>�Vision Build Log</h1>
<h2>Tool Versions:</h2>
IDE-Version: ��Vision V5.41.0.0
Copyright (C) 2024 ARM Ltd and ARM Germany GmbH. All rights reserved.
License Information: 1 <EMAIL>, 11, LIC=IK1BF-6CHK8-IIC5V-P6FEC-8PH1I-E5Z5V
 
Tool Versions:
Toolchain:       MDK-ARM Plus  Version: 5.41.0.0
Toolchain Path:  C:\Keil_v5\ARM\ARMCC\Bin
C Compiler:      Armcc.exe V5.06 update 6 (build 750)
Assembler:       Armasm.exe V5.06 update 6 (build 750)
Linker/Locator:  ArmLink.exe V5.06 update 6 (build 750)
Library Manager: ArmAr.exe V5.06 update 6 (build 750)
Hex Converter:   FromElf.exe V5.06 update 6 (build 750)
CPU DLL:         SARMCM3.DLL V5.41.0.0
Dialog DLL:      DARMSTM.DLL V1.69.1.0
Target DLL:      CMSIS_AGDI.dll V1.33.21.0
Dialog DLL:      TARMSTM.DLL V1.67.1.0
 
<h2>Project:</h2>
C:\Users\<USER>\Desktop\7.31\2025_E_��׼����\Project.uvprojx
Project File Date:  08/01/2025

<h2>Output:</h2>
*** Using Compiler 'V5.06 update 6 (build 750)', folder: 'C:\Keil_v5\ARM\ARMCC\Bin'
Build target 'Target 1'
compiling main.c...
linking...
Program Size: Code=6420 RO-data=268 RW-data=72 ZI-data=2064  
".\Objects\Project.axf" - 0 Error(s), 0 Warning(s).

<h2>Software Packages used:</h2>

Package Vendor: Keil
                https://www.keil.com/pack/Keil.STM32F1xx_DFP.2.4.1.pack
                Keil::STM32F1xx_DFP@2.4.1
                STMicroelectronics STM32F1 Series Device Support, Drivers and Examples

<h2>Collection of Component include folders:</h2>
  C:/Keil_v5/Keil/STM32F1xx_DFP/2.4.1/Device/Include

<h2>Collection of Component Files used:</h2>
Build Time Elapsed:  00:00:01
</pre>
</body>
</html>
