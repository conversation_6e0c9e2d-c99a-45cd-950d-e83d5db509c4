#include "capture.h"

void Capture_Init(void)
{
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB, ENABLE);	//����GPIO��ʱ��
	
	/*GPIO��ʼ��*/
	GPIO_InitTypeDef GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_11;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOB, &GPIO_InitStructure);
	
	GPIO_WriteBit(GPIOB,GPIO_Pin_11,Bit_SET);
}

void Capture_ON(void)
{
	GPIO_WriteBit(GPIOB,GPIO_Pin_11,Bit_RESET);
}

void Capture_OFF(void)
{
	GPIO_WriteBit(GPIOB,GPIO_Pin_11,Bit_SET);
}



