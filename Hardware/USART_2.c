#include "USART_2.h"
/*
数据传输结构 ： 包头 - 标识符（保留） - x误差高八位 - x误差低八位 - y误差高八位 - y误差低八位 - 包尾;

*/

//包头、包尾
#define USART2_Head  0X78
#define USART2_End   0XFC

//数据长度（要设置为两倍数据长度）
#define DATA_MAX    14

//数据缓存区
uint8_t USART2_Data[DATA_MAX];

//数据接收标志位
uint8_t USART2_flag = 0;

//标识符与xy误差
uint8_t TAG;
int16_t ERR_X,ERR_Y;


/****************************************************************************
作	用：串口初始化

参	数：波特率

返回值：无
****************************************************************************/
void USART2_Init(uint32_t Baud_Rate)
{
	/*开启时钟*/
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART2, ENABLE);	//开启USART2的时钟
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);	//开启GPIOA的时钟
	
	/*GPIO初始化*/
	GPIO_InitTypeDef GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOA, &GPIO_InitStructure);					//将PA2引脚初始化为复用推挽输出
	
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_3;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOA, &GPIO_InitStructure);					//将PA3引脚初始化为上拉输入
	
	/*USART初始化*/
	USART_InitTypeDef USART_InitStructure;					//定义结构体变量
	USART_InitStructure.USART_BaudRate = Baud_Rate;				//波特率
	USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;	//硬件流控制，不需要
	USART_InitStructure.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;	//模式，发送模式和接收模式均选择
	USART_InitStructure.USART_Parity = USART_Parity_No;		//奇偶校验，不需要
	USART_InitStructure.USART_StopBits = USART_StopBits_1;	//停止位，选择1位
	USART_InitStructure.USART_WordLength = USART_WordLength_8b;		//字长，选择8位
	USART_Init(USART2, &USART_InitStructure);				//将结构体变量交给USART_Init，配置USART2
	
	/*USARTDMA使能*/
	USART_DMACmd(USART2,USART_DMAReq_Rx,ENABLE);
	
	/*USART使能*/
	USART_Cmd(USART2, ENABLE);								//使能USART2，串口开始运行
}

/****************************************************************************
作	用：串口DMA初始化配置

参	数：无

返回值：无
****************************************************************************/
void USART2_DMA_Init(void)
{
    // 使能DMA1时钟
    RCC_AHBPeriphClockCmd(RCC_AHBPeriph_DMA1, ENABLE);   

    /* USART2 DMA接收初始化 */
    /* 外设配置 */
	DMA_InitTypeDef DMA_InitStructure;
    DMA_InitStructure.DMA_PeripheralBaseAddr = (uint32_t)(&USART2->DR);		// 外设地址
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_Byte; // 数据宽度
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;     	// 外设地址不自增
    /* 存储器配置 */
    DMA_InitStructure.DMA_MemoryBaseAddr = (uint32_t)USART2_Data;            // 存储器地址(存储的数据在这里)
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_Byte;         // 数据宽度
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;                 // 存储器地址自增
    /* 传输方向 - 外设作为源头(SRC)，存储器作为目的地(DST) */
    DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralSRC;
    /* 设置要传输的数据数量 */
    DMA_InitStructure.DMA_BufferSize = DATA_MAX;                             // 自定义的最大接收数据长度
    /* 设置模式 */
    DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;                          // 循环模式
    /* 设置优先级 */
    DMA_InitStructure.DMA_Priority = DMA_Priority_High;
    /* 设置存储器到存储器模式 */
    DMA_InitStructure.DMA_M2M = DMA_M2M_Disable;
    DMA_Init(DMA1_Channel6, &DMA_InitStructure);

	DMA_ITConfig(DMA1_Channel6, DMA_IT_TC | DMA_IT_HT, ENABLE);//设置为半传输中断和全传输中断同时开启
	
	//终端部配置
	NVIC_InitTypeDef NVIC_InitStructure;
    NVIC_InitStructure.NVIC_IRQChannel = DMA1_Channel6_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
	
	DMA_Cmd(DMA1_Channel6,ENABLE);	
}

/****************************************************************************
作	用：串口2发送一位数据

参	数：要发送的数据

返回值：无
****************************************************************************/
void USART2_SendByte(uint8_t Byte)
{
	USART_SendData(USART2, Byte);		//将字节数据写入数据寄存器，写入后USART自动生成时序波形
	while (USART_GetFlagStatus(USART2, USART_FLAG_TXE) == RESET);	//等待发送完成
}




/****************************************************************************
作	用：获取数据更新标志位

参	数：无

返回值：数据更新返回1，未更新返回0
****************************************************************************/
uint8_t USART_Get_RedFlag(void)
{
	if(USART2_flag == 1)
	{
		USART2_flag = 0;
		return 1;
	}
	return 0;
}


//开始识别 - 0x00
void Discern_start(void)
{
	USART2_SendByte(0x00);
}

//结束识别 - 0xff
void Discern_end(void)
{
	USART2_SendByte(0xff);
}

/****************************************************************************
作	用：数据包错误时重置DMA并重新开始识别

参	数：无

返回值：无
****************************************************************************/
void USART2_Reset_And_Restart(void)
{
    // 结束当前识别
    Discern_end();
	
	
    
    // 禁用DMA通道
    DMA_Cmd(DMA1_Channel6, DISABLE);
    
    // 清除DMA缓冲区
    memset(USART2_Data, 0, DATA_MAX);
    
    // 清除所有DMA标志位
    DMA_ClearFlag(DMA1_FLAG_GL6 | DMA1_FLAG_TC6 | DMA1_FLAG_HT6 | DMA1_FLAG_TE6);
    
    // 重新设置DMA传输数量（从0开始接收）
    DMA_SetCurrDataCounter(DMA1_Channel6, DATA_MAX);
    
    // 重新使能DMA通道
    DMA_Cmd(DMA1_Channel6, ENABLE);
    
    // 重新开始识别
    Discern_start();
}


/****************************************************************************
作	用：DMA中断接收、解析数据

参	数：无

返回值：无
****************************************************************************/
void DMA1_Channel6_IRQHandler(void) 
{
	//半传输中断
	if (DMA_GetITStatus(DMA1_IT_HT6) != RESET) 
	{
		if(USART2_Data[0]==USART2_Head && USART2_Data[6]==USART2_End)
		{
			TAG = USART2_Data[1];
			
			ERR_X = (USART2_Data[2] << 8) + USART2_Data[3];
			ERR_Y = (USART2_Data[4] << 8) + USART2_Data[5];
			
			//判断正负号
			if((TAG & 0xF0) == 0xF0)
			{
				ERR_X = -ERR_X;
			}
			if((TAG & 0x0F) == 0x0F)
			{
				ERR_Y = -ERR_Y;
			}
			
			USART2_flag = 1;
		}
		else
			USART2_Reset_And_Restart();
		
		//清除半传输标志位
		DMA_ClearFlag(DMA1_IT_HT6);
	}
	//全传输中断
	else if(DMA_GetITStatus(DMA1_IT_TC6) != RESET)
	{
		if(USART2_Data[7]==USART2_Head && USART2_Data[13]==USART2_End)
		{
			TAG = USART2_Data[8];
			
			ERR_X = (USART2_Data[9] << 8) + USART2_Data[10];
			ERR_Y = (USART2_Data[11] << 8) + USART2_Data[12];
			
			//判断正负号
			if((TAG & 0xF0) == 0xF0)
			{
				ERR_X = -ERR_X;
			}
			if((TAG & 0x0F) == 0x0F)
			{
				ERR_Y = -ERR_Y;
			}
			
			USART2_flag = 1;
		}
		else
			USART2_Reset_And_Restart();
		
		//清除全传输标志位
		DMA_ClearFlag(DMA1_IT_TC6);
	}
}
