#include "key.h"

keys Key[4] = {0};

void Key_Init(void)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	
	// 使能GPIOB时钟
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB, ENABLE);
	
	// 配置PB12-PB15为输入模式，上拉
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_12 | GPIO_Pin_13 | GPIO_Pin_14 | GPIO_Pin_15;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;  // 输入上拉模式
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOB, &GPIO_InitStructure);
}

void Key_clean(void)
{
	memset(Key, 0, sizeof(Key));
}

void Key_port(void)
{
	Key[0].state = GPIO_ReadInputDataBit(GPIOB, GPIO_Pin_12);
	Key[1].state = GPIO_ReadInputDataBit(GPIOB, GPIO_Pin_13);
	Key[2].state = GPIO_ReadInputDataBit(GPIOB, GPIO_Pin_14);
	Key[3].state = GPIO_ReadInputDataBit(GPIOB, GPIO_Pin_15);
	
	for(uint8_t i = 0;i<4;i++)
	{
		switch(Key[i].judge)
		{
			case 0:
			{
				if(Key[i].state == 0)
				{
					Key[i].judge = 1;
				}
			}
			break;
			case 1:
			{
				if(Key[i].state == 0)
				{
					Key[i].judge = 2;
					Key[i].data =1;
					Key[i].time = 1;
				}
				else
				{
					Key[i].judge = 0;
				}
				
			}
			break;
			case 2:
			{
				
				if(Key[i].state == 0)
				{
					Key[i].time++;
				}
				else
				{
					Key[i].judge = 0;
					Key[i].data =2;
					
				}	
			}
			break;
		}
	}
}
