#ifndef __PWM_H
#define __PWM_H

#include "main.h"

// GPIO���Ŷ���
#define STEPPER_STEP1_PIN    GPIO_Pin_0    // PA0 - ���1����
#define STEPPER_STEP2_PIN    GPIO_Pin_6    // PA1 - ���2����

#define STEPPER_DIR1_PIN     GPIO_Pin_1    // PA2 - ���1����
#define STEPPER_DIR2_PIN     GPIO_Pin_7    // PA3 - ���2����
#define STEPPER_GPIO_PORT    GPIOA

// ��ʱ������
#define STEPPER_TIMER        TIM2
#define STEPPER_TIMER_RCC    RCC_APB1Periph_TIM2
#define STEPPER_TIMER_IRQ    TIM2_IRQn

// �ٶȺ�ʱ�����
#define STEPPER_SPEED        3000     // �̶��ٶȣ���/�룩
#define STEPPER_PULSE_WIDTH  5       // �����ȣ�΢�룩

// ������غ궨��
#define DECEL_DISTANCE 1       	 // ���پ��루��������
#define MIN_STEP_HZ	   3000          // ��Ͳ���Ƶ�ʣ�Hz������������������ж�

// ���ĺ�������
void Stepper_Init(void);                        // �������ϵͳ��ʼ��
void Stepper_Motor1_SetPosition(int32_t position);  // ���õ��1Ŀ��λ��
void Stepper_Motor2_SetPosition(int32_t position);  // ���õ��2Ŀ��λ��

#endif
